#   ____  ____   _____
#  / __ \|  _ \ / ____|
# | |  | | |_) | |     ___  _ __ ___
# | |  | |  _ <| |    / _ \| '__/ _ \
# | |__| | |_) | |___| (_) | | |  __/
#  \___\_\____/ \_____\___/|_|  \___|

## FiveM Documentation
## https://aka.cfx.re/server-commands

## QBCore Documentation
## https://docs.qbcore.org

## You CAN edit the following:
endpoint_add_tcp "0.0.0.0:30120"
endpoint_add_udp "0.0.0.0:30120"
sv_maxclients 48
set steam_webApiKey "none"
sets tags "default, deployer, qbcore, qb-core"

## You MAY edit the following:
sv_licenseKey "cfxk_AkOjA8yfqfuq72o51RCl_OXxB6"
sv_hostname "Achraf built with QBCore Framework by JericoFx & <PERSON> & Ham<PERSON>!"
sets sv_projectName "[QBCore Framework] Achraf"
sets sv_projectDesc "An advanced FiveM RP framework including jobs, gangs, housing & more!"
sets locale "root-AQ" # replace with your language and location code, like en-US, pt-BR, etc
load_server_icon myLogo.png
sv_enforceGameBuild 3095 #DLC The Chop Shop
set resources_useSystemChat true
set mysql_connection_string "mysql://root@localhost/QBCore_30B1AD?charset=utf8mb4"

# Voice config
setr voice_useNativeAudio true
setr voice_useSendingRangeOnly true
setr voice_defaultCycle "GRAVE"
setr voice_defaultVolume 0.3
setr voice_enableRadioAnim 1
setr voice_syncData 1

# QBCore locale config
setr qb_locale "en"

# QBCore UseTarget
setr UseTarget false

# These resources will start by default.
ensure mapmanager
ensure chat
ensure spawnmanager
ensure sessionmanager
ensure basic-gamemode
ensure hardcap
ensure baseevents
ensure qb-crawling-death
ensure DLVanillaSWAT
ensure DLDebadged450

# QBCore & Extra stuff
ensure qb-core
ensure [qb]
ensure qb-policecustom
ensure [standalone]
ensure [voice]
ensure [defaultmaps]
ensure [chat]

## Permissions ##
add_ace group.admin command allow # allow all commands
add_principal identifier.fivem:6706393 group.admin #MEDRED
add_principal identifier.discord:780087217825120266 group.admin #MEDRED

# Resources
add_ace resource.qb-core command allow # Allow qb-core to execute commands

# Gods
add_ace qbcore.god command allow # Allow all commands

# Inheritance
add_principal qbcore.god group.admin # Allow gods access to the main admin group used to get all default permissions
add_principal qbcore.god qbcore.admin # Allow gods access to admin commands
add_principal qbcore.admin qbcore.mod # Allow admins access to mod commands
