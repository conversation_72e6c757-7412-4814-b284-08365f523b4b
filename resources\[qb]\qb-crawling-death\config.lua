Config = {}

-- Crawling Death System Configuration
Config.CrawlingTime = 30000 -- Time in milliseconds (30 seconds)
Config.CrawlSpeed = 0.5 -- Speed multiplier for crawling movement
Config.ReviveTime = 5000 -- Time to revive a crawling player (5 seconds)
Config.CooldownTime = 300000 -- Cooldown between crawling states (5 minutes)

-- Animation settings (using more reliable animations)
Config.CrawlAnimDict = 'move_crawl'
Config.CrawlAnim = 'onfront_fwd'
Config.CrawlIdleAnim = 'onfront_idle'
-- Backup animation if move_crawl doesn't work
Config.BackupAnimDict = 'amb@world_human_bum_slumped@male@laying_on_left_side@base'
Config.BackupAnim = 'base'

-- Items that can revive crawling players
Config.ReviveItems = {
    'bandage',
    'firstaid',
    'revive_kit'
}

-- Health settings
Config.CrawlingHealth = 50 -- Health when crawling
Config.RevivedHealth = 75 -- Health when revived from crawling
Config.MinHealthToRevive = 25 -- Minimum health after revival

-- UI Settings
Config.ShowCrawlingInstructions = true
Config.InstructionText = "Use WASD to crawl. You have %s seconds before going unconscious."

-- Debug mode
Config.Debug = false

-- Disable certain actions while crawling
Config.DisableActions = {
    22, -- INPUT_JUMP
    23, -- INPUT_ENTER
    24, -- INPUT_ATTACK
    25, -- INPUT_AIM
    37, -- INPUT_SELECT_WEAPON
    44, -- INPUT_COVER
    45, -- INPUT_RELOAD
    140, -- INPUT_MELEE_ATTACK_LIGHT
    141, -- INPUT_MELEE_ATTACK_HEAVY
    142, -- INPUT_MELEE_ATTACK_ALTERNATE
    143, -- INPUT_MELEE_BLOCK
}

-- Notification settings
Config.Notifications = {
    enterCrawling = "You are critically injured! Crawl to safety or wait for help!",
    crawlingExpired = "You have lost consciousness...",
    beingRevived = "Someone is helping you...",
    revived = "You have been revived!",
    revivedOther = "You successfully revived the player!",
    cannotRevive = "This player cannot be revived right now!",
    onCooldown = "You must wait before entering crawling state again!",
    noReviveItem = "You need a medical item to revive this player!"
}

-- Sound effects (optional)
Config.Sounds = {
    enterCrawling = {
        name = "demo",
        volume = 0.1
    },
    revived = {
        name = "demo",
        volume = 0.2
    }
}
