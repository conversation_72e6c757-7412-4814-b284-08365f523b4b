local QBCore = exports['qb-core']:GetCoreObject()

-- State variables
local isCrawling = false
local crawlingStartTime = 0
local crawlingEndTime = 0
local lastCrawlingTime = 0
local isBeingRevived = false
local crawlThread = nil

-- Debug print
print('[CrawlingDeath] Client script loaded successfully')

-- Direct death event handler as backup
AddEventHandler('gameEventTriggered', function(event, data)
    if event == 'CEventNetworkEntityDamage' then
        local victim, attacker, victimDied, weapon = data[1], data[2], data[4], data[7]
        if not IsEntityAPed(victim) then return end
        if victimDied and NetworkGetPlayerIndexFromPed(victim) == PlayerId() and IsEntityDead(PlayerPedId()) then
            print('[CrawlingDeath] Direct death event detected')
            -- Small delay to ensure ambulance system processes first
            SetTimeout(1000, function()
                if not isCrawling and IsEntityDead(PlayerPedId()) then
                    print('[CrawlingDeath] Triggering crawling state from direct handler')
                    StartCrawling()
                end
            end)
        end
    end
end)

-- Animation and movement
local function LoadAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        RequestAnimDict(dict)
        Wait(5)
    end
end

-- Check if player is on cooldown
local function IsOnCooldown()
    return (GetGameTimer() - lastCrawlingTime) < Config.CooldownTime
end

-- Start crawling state
local function StartCrawling()
    print('[CrawlingDeath] StartCrawling called')

    if isCrawling then
        print('[CrawlingDeath] Already crawling, ignoring')
        return
    end

    if IsOnCooldown() then
        print('[CrawlingDeath] On cooldown, ignoring')
        QBCore.Functions.Notify(Config.Notifications.onCooldown, 'error', 3000)
        return
    end

    local ped = PlayerPedId()
    print('[CrawlingDeath] Starting crawling state for player')
    isCrawling = true
    crawlingStartTime = GetGameTimer()
    crawlingEndTime = crawlingStartTime + Config.CrawlingTime

    -- Set player health and make invincible
    SetEntityHealth(ped, Config.CrawlingHealth)
    SetEntityInvincible(ped, true)

    -- Clear any existing tasks
    ClearPedTasks(ped)

    -- Try to load and play crawling animation, with backup
    LoadAnimDict(Config.CrawlAnimDict)
    if HasAnimDictLoaded(Config.CrawlAnimDict) then
        TaskPlayAnim(ped, Config.CrawlAnimDict, Config.CrawlAnim, 1.0, 1.0, -1, 1, 0, false, false, false)
        print('[CrawlingDeath] Playing crawling animation')
    else
        -- Use backup animation
        print('[CrawlingDeath] Crawling animation not available, using backup')
        LoadAnimDict(Config.BackupAnimDict)
        TaskPlayAnim(ped, Config.BackupAnimDict, Config.BackupAnim, 1.0, 1.0, -1, 1, 0, false, false, false)
    end

    -- Notification and sound
    QBCore.Functions.Notify(Config.Notifications.enterCrawling, 'error', 5000)
    if Config.Sounds.enterCrawling then
        TriggerServerEvent('InteractSound_SV:PlayOnSource', Config.Sounds.enterCrawling.name, Config.Sounds.enterCrawling.volume)
    end

    -- Start crawling thread
    if crawlThread then
        crawlThread = nil
    end

    crawlThread = CreateThread(function()
        while isCrawling do
            local timeLeft = math.max(0, crawlingEndTime - GetGameTimer())

            -- Show instructions
            if Config.ShowCrawlingInstructions and timeLeft > 0 then
                local secondsLeft = math.ceil(timeLeft / 1000)
                local instructionText = string.format(Config.InstructionText, secondsLeft)
                SetTextComponentFormat("STRING")
                AddTextComponentString(instructionText)
                DisplayHelpTextFromStringLabel(0, 0, 1, -1)
            end

            -- Check if time expired
            if timeLeft <= 0 then
                StopCrawling(false)
                break
            end

            -- Handle movement
            HandleCrawlingMovement()

            -- Disable certain actions
            for _, action in pairs(Config.DisableActions) do
                DisableControlAction(0, action, true)
            end

            Wait(0)
        end
    end)

    -- Trigger server event
    TriggerServerEvent('crawling-death:server:enterCrawling')
end

-- Stop crawling state
local function StopCrawling(wasRevived)
    if not isCrawling then return end

    isCrawling = false
    isBeingRevived = false
    lastCrawlingTime = GetGameTimer()

    local ped = PlayerPedId()

    if crawlThread then
        crawlThread = nil
    end

    -- Stop animation
    ClearPedTasks(ped)

    if wasRevived then
        -- Player was revived
        SetEntityHealth(ped, Config.RevivedHealth)
        SetEntityInvincible(ped, false)
        QBCore.Functions.Notify(Config.Notifications.revived, 'success', 3000)

        if Config.Sounds.revived then
            TriggerServerEvent('InteractSound_SV:PlayOnSource', Config.Sounds.revived.name, Config.Sounds.revived.volume)
        end
    else
        -- Time expired, go to normal death state
        QBCore.Functions.Notify(Config.Notifications.crawlingExpired, 'error', 3000)
        TriggerEvent('hospital:client:KillPlayer') -- Trigger normal death
    end

    -- Trigger server event
    TriggerServerEvent('crawling-death:server:exitCrawling', wasRevived)
end

-- Handle crawling movement
function HandleCrawlingMovement()
    local ped = PlayerPedId()

    -- Use a more appropriate crawling animation
    if not IsEntityPlayingAnim(ped, 'move_crawl', 'onfront_fwd', 3) then
        LoadAnimDict('move_crawl')
        TaskPlayAnim(ped, 'move_crawl', 'onfront_fwd', 1.0, 1.0, -1, 1, 0, false, false, false)
    end

    local moveVector = vector3(0.0, 0.0, 0.0)
    local isMoving = false

    -- Forward movement
    if IsControlPressed(0, 32) then -- W
        moveVector = moveVector + GetEntityForwardVector(ped) * Config.CrawlSpeed
        isMoving = true
        -- Play forward crawling animation
        if not IsEntityPlayingAnim(ped, 'move_crawl', 'onfront_fwd', 3) then
            TaskPlayAnim(ped, 'move_crawl', 'onfront_fwd', 2.0, 2.0, -1, 1, 0, false, false, false)
        end
    end

    -- Backward movement
    if IsControlPressed(0, 33) then -- S
        moveVector = moveVector - GetEntityForwardVector(ped) * (Config.CrawlSpeed * 0.5)
        isMoving = true
    end

    -- Left/Right turning
    if IsControlPressed(0, 34) then -- A
        SetEntityHeading(ped, GetEntityHeading(ped) + 1.5)
    elseif IsControlPressed(0, 35) then -- D
        SetEntityHeading(ped, GetEntityHeading(ped) - 1.5)
    end

    -- Apply movement with ground detection
    if moveVector.x ~= 0.0 or moveVector.y ~= 0.0 then
        local currentPos = GetEntityCoords(ped)
        local newPos = currentPos + moveVector * GetFrameTime() * 8.0

        -- Ensure player stays on ground
        local groundZ = GetGroundZFor_3dCoord(newPos.x, newPos.y, newPos.z + 1.0, false)
        if groundZ ~= 0 then
            newPos = vector3(newPos.x, newPos.y, groundZ + 0.1)
        end

        SetEntityCoords(ped, newPos.x, newPos.y, newPos.z, false, false, false, false)
    end

    -- If not moving, play idle crawling animation
    if not isMoving and not IsEntityPlayingAnim(ped, 'move_crawl', 'onfront_idle', 3) then
        TaskPlayAnim(ped, 'move_crawl', 'onfront_idle', 1.0, 1.0, -1, 1, 0, false, false, false)
    end
end

-- Check if player can be revived
local function CanRevivePlayer(targetPed)
    local targetPlayerId = NetworkGetPlayerIndexFromPed(targetPed)
    if targetPlayerId == -1 then return false end

    -- Check if target is in crawling state via server callback
    local canRevive = false
    QBCore.Functions.TriggerCallback('crawling-death:server:canRevive', function(result)
        canRevive = result
    end, GetPlayerServerId(targetPlayerId))

    -- Wait for callback response (with timeout)
    local timeout = 0
    while canRevive == false and timeout < 100 do
        Wait(10)
        timeout = timeout + 1
    end

    return canRevive
end

-- Revive crawling player
local function ReviveCrawlingPlayer(targetPed)
    if isBeingRevived then return end

    local hasReviveItem = false
    for _, item in pairs(Config.ReviveItems) do
        if QBCore.Functions.HasItem(item) then
            hasReviveItem = true
            break
        end
    end

    if not hasReviveItem then
        QBCore.Functions.Notify(Config.Notifications.noReviveItem, 'error', 3000)
        return
    end

    local targetPlayerId = GetPlayerServerId(NetworkGetPlayerIndexFromPed(targetPed))

    QBCore.Functions.Progressbar('revive_crawling', 'Reviving player...', Config.ReviveTime, false, true, {
        disableMovement = false,
        disableCarMovement = false,
        disableMouse = false,
        disableCombat = true,
    }, {
        animDict = 'mini@cpr@char_a@cpr_str',
        anim = 'cpr_pumpchest',
        flags = 33,
    }, {}, {}, function() -- Done
        StopAnimTask(PlayerPedId(), 'mini@cpr@char_a@cpr_str', 'cpr_pumpchest', 1.0)
        TriggerServerEvent('crawling-death:server:revivePlayer', targetPlayerId)
        QBCore.Functions.Notify(Config.Notifications.revivedOther, 'success', 3000)
    end, function() -- Cancel
        StopAnimTask(PlayerPedId(), 'mini@cpr@char_a@cpr_str', 'cpr_pumpchest', 1.0)
    end)
end

-- Events
RegisterNetEvent('crawling-death:client:enterCrawling', function()
    print('[CrawlingDeath] Received enterCrawling event')
    StartCrawling()
end)

RegisterNetEvent('crawling-death:client:exitCrawling', function(wasRevived)
    StopCrawling(wasRevived)
end)

RegisterNetEvent('crawling-death:client:revivePlayer', function()
    local player, distance = QBCore.Functions.GetClosestPlayer()
    if player ~= -1 and distance < 3.0 then
        local targetPed = GetPlayerPed(player)
        if CanRevivePlayer(targetPed) then
            ReviveCrawlingPlayer(targetPed)
        else
            QBCore.Functions.Notify(Config.Notifications.cannotRevive, 'error', 3000)
        end
    else
        QBCore.Functions.Notify('No player nearby!', 'error', 3000)
    end
end)

-- Export functions
exports('IsCrawling', function()
    return isCrawling
end)

exports('StartCrawling', function()
    StartCrawling()
end)

exports('StopCrawling', function(wasRevived)
    StopCrawling(wasRevived or false)
end)

-- Test command for manual triggering
RegisterCommand('testcrawl', function()
    print('[CrawlingDeath] Manual test command triggered')
    StartCrawling()
end, false)
